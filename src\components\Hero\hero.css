.hero {
  height: 100vh;
  position: relative;
  overflow: hidden;
  background-color: #9C4DF4;
  border-radius: 0 0 50% 50% / 0 0 100px 100px;
  margin-bottom: 50px;
}

.hero::after {
  content: '';
  position: absolute;
  bottom: -50px;
  left: 0;
  width: 100%;
  height: 100px;
  background-color: #F7F5FA;
  z-index: -1;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}





.floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(5px);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.hero-image-container {
  position: relative;
}

.hero-image {
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.2));
  transition: all 0.5s ease;
}

.hero-image:hover {
  transform: translateY(-10px);
}

.hero-background-shape {
  position: absolute;
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

/* تحسين الشكل الدائري خلف صورة البطل */
.hero-background-circle {
  position: absolute;
  width: 450px;
  height: 450px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  top: 50%;
  right: 20%;
  transform: translate(0, -50%);
  z-index: 1;
  overflow: hidden;
}

.hero-background-circle::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(156, 77, 244, 0.4) 70%);
  border-radius: 50%;
}

.hero-background-circle::after {
  content: '';
  position: absolute;
  width: 80%;
  height: 80%;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  top: 10%;
  left: 10%;
  animation: rotate 30s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* إضافة نقاط زخرفية */
.decorative-dot {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 1;
}

.dot-1 {
  width: 20px;
  height: 20px;
  top: 20%;
  right: 15%;
  animation: float 4s ease-in-out infinite;
}

.dot-2 {
  width: 15px;
  height: 15px;
  bottom: 25%;
  right: 30%;
  animation: float 6s ease-in-out infinite 1s;
}

.dot-3 {
  width: 10px;
  height: 10px;
  top: 40%;
  right: 10%;
  animation: float 5s ease-in-out infinite 0.5s;
}
