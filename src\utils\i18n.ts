import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import en from '../locales/en.json';
import ar from '../locales/ar.json';
// the translations
// (tip move them in a JSON file and import them,
// or even better, manage them separated from your code: https://react.i18next.com/guides/multiple-translation-files)

const storedLanguage = localStorage.getItem('language') || 'en'; 

i18n

  .use(initReactI18next) 
  .init({
    resources:{
        en:{
            translation: en
        },
        ar:{
            translation: ar
        },
    },
    lng: storedLanguage,
    fallbackLng: 'en',
    detection: {
        order: ['localStorage', 'navigator'],  
        caches: ['localStorage'],  
      },
  });

 
  export default i18n;