import img1 from '../assets/Serves/Group 106 (1).png'
import img2 from '../assets/Serves/Group 92.png'
import img3 from '../assets/Serves/Group 122 (1).png'
import img1RTL from '../assets/Serves/Group 106 (2).png'
export const benefitsData =

[
    {
        img : img1 ,
        imgRTL: img1RTL, 
        flex : "flex-row",
        h: "One-on-One Discussions",
        p : "Teachers  can talk with students privately without leaving the platform environment."
    },
    {
        img : img2 ,
        imgRTL: img1RTL, 
        flex : "flex-row-reverse",
        h: "Assessments, Quizzes, Tests",
        p : "Easily launch live assignments, quizzes, and tests.Student results are automatically entered in the online gradebook."
    },
    {
        img : img3 ,
        imgRTL: img1RTL,
        flex : "flex-row", 
        h: "Tools For Teachers And Learners",
        p : "Class has a dynamic set of teaching tools built to be deployed and used during class.Teachers can handout assignments in real-time for students to complete and submit."
    },

]