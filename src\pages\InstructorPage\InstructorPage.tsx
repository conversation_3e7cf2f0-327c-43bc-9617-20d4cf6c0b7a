// import FifthInstrucor from "../../components/FifthInstrucor/FifthInstrucor";
import FourthInstrucor from "../../components/FourthInstrucor/FourthInstrucor";
import HeroInstructure from "../../components/HeroInstructure/HeroInstructure";
// import SecondInstructor from "../../components/SecondInstructor/SecondInstructor";
import ThirdInstructor from "../../components/ThirdInstructor/ThirdInstructor";

export default function InstructorPage() {
  return (
    <section className=" ">
     <HeroInstructure/>
     {/* <SecondInstructor/> */}
     <ThirdInstructor/>
     <FourthInstrucor/>
     {/* <FifthInstrucor 
     color={" bg-inherit "}
      title={"Instructor rules & regulations"} 
      desc={"Sed auctor, nisl non elementum ornare, turpis orci consequat arcu, at iaculis quam leo nec libero. Aenean mollis turpis velit, id laoreet sem luctus in. Etiam et egestas lorem. "} 
      li1={"Sed ullamcorper libero quis condimentum pellentesque."} 
      li2={"Nam leo tortor, tempus et felis non."} 
      li3={"Porttitor faucibus erat. Integer eget purus non massa ultricies pretium ac sed eros."} 
      li4={"Vestibulum ultrices commodo tellus. Etiam eu lectus sit amet turpi."} 
      pic1={pic1}    /> */}

      {/* sixth section */}
     {/* <FifthInstrucor 
     color={" bg-pink-100 flex-row-reverse"}
      title={"Don’t worry we’re always here to help you"} 
      desc={"Mauris aliquet ornare tortor, ut mollis arcu luctus quis. Phasellus nec augue malesuada, sagittis ligula vel, faucibus metus. Nam viverra metus eget nunc dignissim. "} 
      li1={"Sed nec dapibus orci integer nisl turpis, eleifend sit amet aliquam vel."} 
      li2={"Those who are looking to reboot their work life and try a new profession that."} 
      li3={"Nunc auctor consequat lorem, in posuere enim hendrerit sed."} 
      li4={"Duis ornare enim ullamcorper congue."} 
      pic1={pic2}    /> */}

      {/* <SeventhInstrucor/> */}
    </section>
  )
}
//this is comment 21 to test code 